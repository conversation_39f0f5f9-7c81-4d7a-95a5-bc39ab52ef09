 name: pytorch_y
 channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - defaults
 dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=4.5
  - blas=1.0
  - bzip2=1.0.8
  - ca-certificates=2022.3.29
  - certifi=2021.10.8
  - cudatoolkit=11.1.1
  # - freetype=2.11.0
  - giflib=5.2.1
  # - gmp=6.2.1
  # - gnutls=3.6.15
  - intel-openmp=2021.4.0
  - jpeg=9b
  - lame=3.100
  - lcms2=2.12
  # - ld_impl_linux-64=2.35.1
  - libffi=3.3
  # - libgcc-ng=9.3.0
  # - libgomp=9.3.0
  # - libidn2=2.3.2
  - libopus=1.3.1
  - libpng=1.6.37
  # - libstdcxx-ng=9.3.0
  # - libtasn1=4.16.0
  - libtiff=4.2.0
  # - libunistring=0.9.10
  - libuv=1.40.0
  # - libvpx=1.7.0
  - libwebp=1.2.0
  - libwebp-base=1.2.0
  - lz4-c=1.9.3
  - mkl=2021.4.0
  - mkl-service=2.4.0
  - mkl_fft=1.3.1
  - mkl_random=1.2.2
  # - ncurses=6.3
  # - nettle=3.7.3
  - ninja=1.10.2
  - numpy-base=1.21.2
  - olefile=0.46
  # - openh264=2.1.0
  - openssl=1.1.1n
  - pip=21.2.4
  - portaudio=19.6.0
  - pyaudio=0.2.11
  - python=3.8.12
  - pytorch=1.8.0
  # - readline=8.1
  - setuptools=58.0.4
  - six=1.16.0
  - sqlite=3.36.0
  - tk=8.6.11
  - torchaudio=0.8.0
  - torchvision=0.9.0
  - typing_extensions=********
  - wheel=0.37.0
  # - x264=1!157.20191217
  - xz=5.2.5
  - zlib=1.2.11
  - zstd=1.4.9
  - pip:
    - av==9.2.0
    - charset-normalizer==2.0.12
    - click==8.1.2
    # - clip==1.0
    - cycler==0.11.0
    - decorator==4.4.2
    - docker-pycreds==0.4.0
    - einops==0.6.0
    - ffmpeg==1.4
    - fonttools==4.28.1
    - ftfy==6.1.1
    - fvcore==0.1.5.post20220414
    - gensim==4.1.2
    - gitdb==4.0.9
    - gitpython==3.1.27
    - h5py==3.7.0
    - idna==3.3
    - imageio==2.16.1
    - imageio-ffmpeg==0.4.5
    - imutils==0.5.4
    - iopath==0.1.9
    - joblib==1.1.0
    - kiwisolver==1.3.2
    - matplotlib==3.5.0
    - moviepy==1.0.3
    - numpy==1.21.4
    - opencv-contrib-python==********
    - opencv-python==********
    - packaging==21.3
    - pandas==1.3.4
    - pathtools==0.1.2
    - pillow==8.4.0
    - portalocker==2.4.0
    - proglog==0.1.9
    - promise==2.3
    - protobuf==3.20.0
    - psutil==5.9.0
    - pyparsing==3.0.6
    - python-dateutil==2.8.2
    - pytz==2021.3
    - pyyaml==6.0
    - regex==2022.9.13
    - requests==2.27.1
    - scikit-learn==1.0.1
    - scipy==1.7.2
    - seaborn==0.12.0
    - sentry-sdk==1.5.10
    - setproctitle==1.2.3
    - setuptools-scm==6.3.2
    - shortuuid==1.0.8
    - simplejson==3.17.6
    - smart-open==5.2.1
    - smmap==5.0.0
    - tabulate==0.8.9
    - termcolor==1.1.0
    - thop==0.0.31-2005241907
    - threadpoolctl==3.0.0
    - tomli==1.2.2
    - tqdm==4.63.0
    - urllib3==1.26.9
    - wandb==0.12.14
    - wcwidth==0.2.5
    - xlwt==1.3.0
    - yacs==0.1.8
 prefix: /data/pyj/anaconda/envs/pytorch
