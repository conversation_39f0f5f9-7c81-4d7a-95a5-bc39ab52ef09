import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
from torch import FloatTensor  # 导入FloatTensor类型
from torch.nn.parameter import Parameter  # 导入Parameter类型
from scipy.spatial.distance import pdist, squareform  # 导入距离计算函数
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
import math  # 导入数学库


class DistanceAdj(nn.Module):
    def __init__(self, sigma, bias):
        super(DistanceAdj, self).__init__()
        # self.sigma = sigma
        # self.bias = bias
        self.w = nn.Parameter(torch.FloatTensor(1))  # 可学习参数w
        self.b = nn.Parameter(torch.FloatTensor(1))  # 可学习参数b
        self.w.data.fill_(sigma)  # 初始化w
        self.b.data.fill_(bias)  # 初始化b

    def forward(self, batch_size, seq_len):
        arith = np.arange(seq_len).reshape(-1, 1)  # 生成序列索引
        dist = pdist(arith, metric='cityblock').astype(np.float32)  # 计算曼哈顿距离
        dist = torch.from_numpy(squareform(dist)).cuda()  # 转为方阵并搬到GPU
        # dist = torch.exp(-self.sigma * dist ** 2)
        dist = torch.exp(-torch.abs(self.w * dist ** 2 - self.b))  # 距离邻接权重
        dist = torch.unsqueeze(dist, 0).repeat(batch_size, 1, 1)  # 扩展到batch

        return dist  # [batch, seq_len, seq_len]


class TCA(nn.Module):
    def __init__(self, d_model, dim_k, dim_v, n_heads, norm=None):
        super(TCA, self).__init__()
        self.dim_v = dim_v  # value通道数
        self.dim_k = dim_k  # key/query通道数
        self.n_heads = n_heads  # 注意力头数
        self.norm = norm  # 是否归一化

        self.q = nn.Linear(d_model, dim_k)  # query投影
        self.k = nn.Linear(d_model, dim_k)  # key投影
        self.v = nn.Linear(d_model, dim_v)  # value投影
        self.o = nn.Linear(dim_v, d_model)  # 输出投影

        self.norm_fact = 1 / math.sqrt(dim_k)  # 缩放因子
        self.alpha = nn.Parameter(torch.tensor(0.))  # 可学习融合系数
        self.act = nn.Softmax(dim=-1)  # softmax激活

    def forward(self, x, mask, adj=None):
        Q = self.q(x).view(-1, x.shape[0], x.shape[1], self.dim_k // self.n_heads)  # [n_head, batch, seq, dim_k//n_head]
        K = self.k(x).view(-1, x.shape[0], x.shape[1], self.dim_k // self.n_heads)
        V = self.v(x).view(-1, x.shape[0], x.shape[1], self.dim_v // self.n_heads)

        if adj is not None:
            g_map = torch.matmul(Q, K.permute(0, 1, 3, 2)) * self.norm_fact + adj  # 全局注意力+邻接
        else:
            g_map = torch.matmul(Q, K.permute(0, 1, 3, 2)) * self.norm_fact  # 全局注意力
        l_map = g_map.clone()
        l_map = l_map.masked_fill_(mask.data.eq(0), -1e9)  # 局部mask

        g_map = self.act(g_map)  # 全局softmax
        l_map = self.act(l_map)  # 局部softmax
        glb = torch.matmul(g_map, V).view(x.shape[0], x.shape[1], -1)  # 全局加权和
        lcl = torch.matmul(l_map, V).view(x.shape[0], x.shape[1], -1)  # 局部加权和

        alpha = torch.sigmoid(self.alpha)  # 融合系数
        tmp = alpha * glb + (1 - alpha) * lcl  # 全局与局部融合
        if self.norm:
            tmp = torch.sqrt(F.relu(tmp)) - torch.sqrt(F.relu(-tmp))  # power norm
            tmp = F.normalize(tmp)  # l2 norm
        tmp = self.o(tmp).view(-1, x.shape[1], x.shape[2])  # 输出投影
        return tmp  # [batch, seq, d_model]
