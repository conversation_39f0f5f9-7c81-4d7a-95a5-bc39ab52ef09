import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
from torch import FloatTensor  # 导入FloatTensor类型
from torch.nn.parameter import Parameter  # 导入Parameter类型
from scipy.spatial.distance import pdist, squareform  # 导入距离计算函数
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
import math  # 导入数学库


class DistanceAdj(nn.Module):
    def __init__(self, sigma, bias):
        super(DistanceAdj, self).__init__()
        # self.sigma = sigma
        # self.bias = bias
        self.w = nn.Parameter(torch.FloatTensor(1))  # 可学习参数w
        self.b = nn.Parameter(torch.FloatTensor(1))  # 可学习参数b
        self.w.data.fill_(sigma)  # 初始化w
        self.b.data.fill_(bias)  # 初始化b

    def forward(self, batch_size, seq_len):
        arith = np.arange(seq_len).reshape(-1, 1)  # 生成序列索引
        dist = pdist(arith, metric='cityblock').astype(np.float32)  # 计算曼哈顿距离
        dist = torch.from_numpy(squareform(dist)).cuda()  # 转为方阵并搬到GPU
        # dist = torch.exp(-self.sigma * dist ** 2)
        dist = torch.exp(-torch.abs(self.w * dist ** 2 - self.b))  # 距离邻接权重
        dist = torch.unsqueeze(dist, 0).repeat(batch_size, 1, 1)  # 扩展到batch

        return dist  # [batch, seq_len, seq_len]


class TCA(nn.Module):
    def __init__(self, d_model, dim_k, dim_v, n_heads, norm=None):
        super(TCA, self).__init__()
        self.dim_v = dim_v  # value通道数
        self.dim_k = dim_k  # key/query通道数
        self.n_heads = n_heads  # 注意力头数
        self.norm = norm  # 是否归一化
        self.d_model = d_model

        # 多头注意力投影
        self.q = nn.Linear(d_model, dim_k)  # query投影
        self.k = nn.Linear(d_model, dim_k)  # key投影
        self.v = nn.Linear(d_model, dim_v)  # value投影
        self.o = nn.Linear(dim_v, d_model)  # 输出投影

        # 增强的融合机制
        self.norm_fact = 1 / math.sqrt(dim_k // n_heads)  # 修正缩放因子
        self.alpha = nn.Parameter(torch.tensor(0.5))  # 可学习融合系数，初始化为0.5
        self.beta = nn.Parameter(torch.tensor(0.1))   # 新增：邻接权重调节参数

        # 自适应温度参数 - 动态调整注意力分布的锐度
        self.temperature = nn.Parameter(torch.tensor(1.0))

        # 简化的特征增强层
        self.feature_enhance = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.ReLU(inplace=True)
        )

        self.act = nn.Softmax(dim=-1)  # softmax激活
        self.dropout = nn.Dropout(0.1)  # 添加dropout防止过拟合

    def forward(self, x, mask, adj=None):
        batch_size, seq_len, _ = x.shape

        # 简化的特征增强
        x_enhanced = x + self.feature_enhance(x)

        # 多头注意力计算
        Q = self.q(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_k // self.n_heads)
        K = self.k(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_k // self.n_heads)
        V = self.v(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_v // self.n_heads)

        # 计算注意力分数，应用自适应温度
        temperature = torch.clamp(self.temperature, min=0.1, max=2.0)  # 限制温度范围
        attn_scores = torch.matmul(Q, K.permute(0, 1, 3, 2)) * self.norm_fact / temperature

        # 全局注意力图
        if adj is not None:
            # 使用可学习的beta参数调节邻接矩阵的影响
            beta = torch.sigmoid(self.beta)
            g_map = attn_scores + beta * adj.unsqueeze(0)  # 广播邻接矩阵到多头
        else:
            g_map = attn_scores

        # 局部注意力图（应用mask）
        l_map = g_map.clone()
        l_map = l_map.masked_fill_(mask.data.eq(0), -1e9)

        # 应用softmax并添加dropout
        g_map = self.dropout(self.act(g_map))  # 全局softmax + dropout
        l_map = self.dropout(self.act(l_map))  # 局部softmax + dropout

        # 计算加权特征
        glb = torch.matmul(g_map, V).view(batch_size, seq_len, -1)  # 全局加权和
        lcl = torch.matmul(l_map, V).view(batch_size, seq_len, -1)  # 局部加权和

        # 自适应融合全局和局部特征
        alpha = torch.sigmoid(self.alpha)  # 融合系数

        # 增强的融合策略：考虑特征的重要性
        glb_weight = torch.mean(torch.abs(glb), dim=-1, keepdim=True)  # 全局特征重要性
        lcl_weight = torch.mean(torch.abs(lcl), dim=-1, keepdim=True)  # 局部特征重要性
        total_weight = glb_weight + lcl_weight + 1e-8  # 避免除零

        # 动态权重调整
        dynamic_alpha = alpha + 0.1 * (glb_weight / total_weight - 0.5)
        dynamic_alpha = torch.clamp(dynamic_alpha, 0.0, 1.0)

        tmp = dynamic_alpha * glb + (1 - dynamic_alpha) * lcl  # 动态融合

        # 改进的归一化策略
        if self.norm:
            # 使用更稳定的归一化方法
            tmp = F.layer_norm(tmp, tmp.shape[-1:])  # layer norm
            tmp = torch.sqrt(F.relu(tmp)) - torch.sqrt(F.relu(-tmp))  # power norm
            tmp = F.normalize(tmp, p=2, dim=-1)  # l2 norm

        # 输出投影
        tmp = self.o(tmp)  # [batch, seq, d_model]

        # 残差连接
        output = x + tmp

        return output  # [batch, seq, d_model]
