import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
from torch import FloatTensor  # 导入FloatTensor类型
from torch.nn.parameter import Parameter  # 导入Parameter类型
from scipy.spatial.distance import pdist, squareform  # 导入距离计算函数
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
import math  # 导入数学库


class DistanceAdj(nn.Module):
    def __init__(self, sigma, bias):
        super(DistanceAdj, self).__init__()
        # self.sigma = sigma
        # self.bias = bias
        self.w = nn.Parameter(torch.FloatTensor(1))  # 可学习参数w
        self.b = nn.Parameter(torch.FloatTensor(1))  # 可学习参数b
        self.w.data.fill_(sigma)  # 初始化w
        self.b.data.fill_(bias)  # 初始化b

    def forward(self, batch_size, seq_len):
        arith = np.arange(seq_len).reshape(-1, 1)  # 生成序列索引
        dist = pdist(arith, metric='cityblock').astype(np.float32)  # 计算曼哈顿距离
        dist = torch.from_numpy(squareform(dist)).cuda()  # 转为方阵并搬到GPU
        # dist = torch.exp(-self.sigma * dist ** 2)
        dist = torch.exp(-torch.abs(self.w * dist ** 2 - self.b))  # 距离邻接权重
        dist = torch.unsqueeze(dist, 0).repeat(batch_size, 1, 1)  # 扩展到batch

        return dist  # [batch, seq_len, seq_len]


class TCA(nn.Module):
    def __init__(self, d_model, dim_k, dim_v, n_heads, norm=None):
        super(TCA, self).__init__()
        self.dim_v = dim_v  # value通道数
        self.dim_k = dim_k  # key/query通道数
        self.n_heads = n_heads  # 注意力头数
        self.norm = norm  # 是否归一化
        self.d_model = d_model

        # 多头注意力投影
        self.q = nn.Linear(d_model, dim_k)  # query投影
        self.k = nn.Linear(d_model, dim_k)  # key投影
        self.v = nn.Linear(d_model, dim_v)  # value投影
        self.o = nn.Linear(dim_v, d_model)  # 输出投影

        # 增强的融合机制
        self.norm_fact = 1 / math.sqrt(dim_k // n_heads)  # 修正缩放因子
        self.alpha = nn.Parameter(torch.tensor(0.5))  # 可学习融合系数，初始化为0.5
        self.beta = nn.Parameter(torch.tensor(0.1))   # 新增：邻接权重调节参数

        # 自适应温度参数 - 动态调整注意力分布的锐度
        self.temperature = nn.Parameter(torch.tensor(1.0))

        # 极简特征增强
        self.feature_enhance = nn.Linear(d_model, d_model)

        self.act = nn.Softmax(dim=-1)  # softmax激活
        self.dropout = nn.Dropout(0.1)  # 添加dropout防止过拟合

    def forward(self, x, mask, adj=None):
        batch_size, seq_len, _ = x.shape

        # 极简特征增强
        x_enhanced = x + F.relu(self.feature_enhance(x), inplace=True)

        # 高效多头注意力计算
        Q = self.q(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_k // self.n_heads)
        K = self.k(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_k // self.n_heads)
        V = self.v(x_enhanced).view(self.n_heads, batch_size, seq_len, self.dim_v // self.n_heads)

        # 简化的注意力分数计算
        temperature = torch.clamp(self.temperature, min=0.5, max=1.5)  # 缩小范围
        attn_scores = torch.matmul(Q, K.permute(0, 1, 3, 2)) * self.norm_fact / temperature

        # 简化的邻接处理
        if adj is not None:
            beta = torch.sigmoid(self.beta)
            g_map = attn_scores + beta * adj.unsqueeze(0)
        else:
            g_map = attn_scores

        # 局部注意力图
        l_map = g_map.masked_fill_(mask.data.eq(0), -1e9)

        # 高效softmax计算
        g_map = F.softmax(g_map, dim=-1)
        l_map = F.softmax(l_map, dim=-1)

        # 计算加权特征
        glb = torch.matmul(g_map, V).view(batch_size, seq_len, -1)
        lcl = torch.matmul(l_map, V).view(batch_size, seq_len, -1)

        # 简化融合策略
        alpha = torch.sigmoid(self.alpha)
        tmp = alpha * glb + (1 - alpha) * lcl

        # 简化归一化
        if self.norm:
            tmp = F.normalize(tmp, p=2, dim=-1)

        # 输出投影和残差连接
        output = x + self.o(tmp)

        return output
