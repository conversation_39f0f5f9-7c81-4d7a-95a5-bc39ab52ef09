<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="908de975-6488-4ed8-baf1-e9b770758e52" name="变更" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2nroNDKFZCPgTDUfPNp99GrqxZ0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\project\PEL4VAD-master (2)\PEL4VAD-master\PEL4VAD-master\PEL4VAD-master" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="908de975-6488-4ed8-baf1-e9b770758e52" name="变更" comment="" />
      <created>1729740513932</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1729740513932</updated>
      <workItem from="1729740515039" duration="73000" />
      <workItem from="1730012568668" duration="74000" />
      <workItem from="1745923646526" duration="21539000" />
      <workItem from="1746700076850" duration="96000" />
      <workItem from="1746700187416" duration="45511000" />
      <workItem from="1747735632733" duration="65520000" />
      <workItem from="1752999398926" duration="1736000" />
      <workItem from="1753001410431" duration="14092000" />
      <workItem from="1754273434688" duration="1813000" />
      <workItem from="1754275284808" duration="26000" />
      <workItem from="1754275316163" duration="4498000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>