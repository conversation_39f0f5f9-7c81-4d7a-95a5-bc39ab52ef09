{"normal event": {"normal event": 1.0}, "cycling": {"cycling": 1.0, "sport": 0.321, "motorcycling": 0.658, "dune cycling": 0.125, "bicycling": 0.891, "breath heavily": 0.056}, "chasing": {"stalking": 0.47, "chasing pitched ball": 1.0, "pursuing enemy": 0.598, "locomotion process": 0.074, "chasing thrown ball": 1.0, "chasing hit ball": 1.0, "chasing": 1.0}, "handcart": {"handcart": 1.0, "shopping cart": 0.132, "wheeled vehicle": 0.468, "laundry cart": 0.152, "serving cart": 0.006, "hand truck": 0.301, "applecart": 0.442, "barrow": 0.331, "haul": 0.248, "baby buggy": 0.089}, "fighting": {"fighting": 1.0, "wrestling": 0.409, "tugging": 0.312, "floundering": 0.382, "attempt to do": 0.093, "advancing into battle": 0.254, "an arena": 0.021, "reveal true intentions": 0.012, "having a party": 0.208, "throw a punch": 0.062, "punching someone": 0.37, "punch your enemy": 0.249, "call each other names": 0.058}, "skateboarding": {"skateboarding": 1.0, "skating": 0.633, "boy skateboarding": 0.12, "event": 0.09, "ride": 0.238, "recreational board riding activity": 0.242, "skateboarding trick": 1.0, "skateboarding in skatepark": 1.0, "recreational activity": 0.242, "outdoor activity": 0.172, "freestyle skateboarding": 0.505, "boardsport": 0.369, "street skateboarding": 0.288}, "vehicle": {"vehicle": 1.0, "rocket": 0.125, "bumper car": 0.285, "canoe": 0.132, "craft": 0.166, "wheeled vehicle": 0.576, "substance": 0.141, "sled": 0.109, "conveyance": 0.274, "skibob": 0.043, "electric vehicle": 0.082, "steamroller": 0.106, "medium": 0.102, "military vehicle": 0.121, "watercraft": 0.381, "neighborhood electric vehicle": 0.048, "lorry": 0.56, "bmw": 0.534, "airplane": 0.156, "new energy vehicle": 0.013, "physical object": 0.079, "bicycle": 0.219}, "running": {"running": 1.0, "administration": 0.171, "administrivia": 0.212, "operation": 0.127, "conscious activity": 0.05, "starting": 0.228, "run cycle": 0.676, "an energetic activity": 0.07, "mowing": 0.189, "running automobile": 0.759, "ambulating": 0.198, "electric alarm clock ringing": 0.03, "slower than biking": 0.15, "running forward": 0.759, "running on two legs": 0.759, "political behavior": 0.067, "locomotion": 0.229, "physical event": 0.137, "event": 0.017, "sprinting": 0.616, "long running": 0.058, "slower than driving": 0.15, "running backwards": 0.759, "inputting data": 0.108, "releasing from confinement": 0.201, "running on four legs": 0.759, "fun": 0.053, "competing in a political race": 0.297, "to get away": 0.104, "influencing voters": 0.049, "escaping police": 0.262, "exercise": 0.143, "obtaining office": 0.119, "getting somewhere fast": 0.169, "losing weight": 0.255, "speed": 0.226, "escaping": 0.262, "track events": 0.194, "getting there fast on foot": 0.169, "getting away from everything": 0.169, "go faster than walking": 0.095, "getting a public office": 0.169, "getting somewhere": 0.169, "getting someplace fast": 0.169, "an olympic sport": 0.07, "getting someplace faster than walking": 0.169, "increasing the pace": 0.083, "escaping from danger": 0.262, "getting votes": 0.169, "getting up early": 0.169, "keeping fit": 0.238, "getting aerobic exercise": 0.169, "to chase after": 0.104, "competition": 0.104, "sport": 0.081, "getting away from something": 0.169, "getting exercise": 0.169, "getting places faster": 0.169, "exersize": 0.148, "moving by foot quickly": 0.126, "getting voted in": 0.169, "pleasure": 0.049, "going for a run": 0.248, "going fast": 0.248, "getting away from muggers": 0.169, "wind you": 0.115, "sweating": 0.331, "tiredness": 0.151, "shortness of breath": 0.091, "falling down": 0.16, "falling": 0.167, "getting a cramp": 0.169, "getting tired": 0.169, "injuries": 0.113, "exhaustion": 0.149, "moving fast": 0.126, "playing frisbee": 0.218, "an accelerated heart rate": 0.07, "sore feet": 0.163, "calluses": 0.11, "proving your physical endurance": 0.07, "becoming tired": 0.072, "becoming out of breathe": 0.072, "leg cramps": 0.075, "sweat": 0.126, "getting out of breath": 0.169, "getting somewhere more quickly": 0.169, "accidents": 0.009, "going faster than walking": 0.248, "be out of breath": 0.021, "breathlessness": 0.209, "running out of breath": 0.759, "weight loss": 0.141, "cramps": 0.148, "stitches": 0.192, "getting somewhere faster": 0.169, "injury": 0.019, "playing sports": 0.218, "getting fit": 0.169, "getting some physical activity": 0.169, "trip": 0.06, "listen to music": 0.027, "breathing hard": 0.003, "dehydration": 0.048, "going jogging": 0.248, "breath": 0.084, "play soccer": 0.021, "pant": 0.05, "twist your ankle": 0.072, "to become winded": 0.104, "playing sport": 0.218, "scoring a homer": 0.134, "playing baseball": 0.218, "getting away with a crime": 0.169, "get a blister on your foot": 0.074, "trip over a fallen tree branch": 0.037, "running twenty six miles": 0.759, "playing": 0.218, "tie your shoes": 0.11, "playing lacrosse": 0.218, "trip over": 0.037}, "jumping": {"jumping": 1.0, "broad jump": 0.059, "high jump": 0.124, "track and field": 0.096, "an activity": 0.046, "horse jumping": 0.185, "jumping over": 0.776, "jumping overboard": 0.776, "event": 0.095, "locomotion": 0.215, "landing": 0.122, "play basketball": 0.014}, "wandering": {"wandering": 1.0, "drifting": 0.426, "travel": 0.238}, "lifting": {"lifting": 1.0, "raising automobile": 0.381, "going up": 0.137, "lifting opponent": 0.807, "lifting onto shoulders": 0.807, "moving": 0.381, "type lifting": 0.044}, "robbery": {"robbery": 1.0, "larceny": 0.701, "rolling": 0.018, "caper": 0.307, "highway robbery": 0.093, "highjacking": 0.407, "dacoity": 0.422, "heist": 0.73, "armed robbery": 0.388, "piracy": 0.264, "terror": 0.064}, "climbing over": {"climbing over": 1.0, "climbing": 0.96, "passing by": 0.115, "going over": 0.249}, "throwing": {"throwing": 1.0, "propelling object": 0.308, "casting fishing line": 0.166, "beaning": 0.597, "stickball delivery": 0.211, "skipping stone": 0.598, "throwing discus": 0.819, "human throwing": 0.004, "throwing spear": 0.819, "throwing flowers": 0.819, "movement": 0.068, "pitching": 0.459, "throwing die": 0.819, "dwarf tossing": 0.064, "confetti": 0.283, "an egg": 0.028, "playing ball": 0.334}}