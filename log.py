import logging  # 导入logging模块


def get_logger(filename, verbosity=1, name=None):
    level_dict = {0: logging.DEBUG, 1: logging.INFO, 2: logging.WARNING}  # 日志等级映射
    # formatter = logging.Formatter(
    #     "[%(filename)s][%(levelname)s] %(message)s"
    # )
    formatter = logging.Formatter(
        "[%(asctime)s][%(filename)s][line:%(lineno)d][%(levelname)s] %(message)s"  # 日志格式：时间、文件、行号、等级、信息
    )
    logger = logging.getLogger(name)  # 获取logger对象
    logger.setLevel(level_dict[verbosity])  # 设置日志等级

    fh = logging.FileHandler(filename, "a")  # 文件日志处理器，追加模式
    fh.setFormatter(formatter)  # 设置格式
    logger.addHandler(fh)  # 添加到logger

    sh = logging.StreamHandler()  # 控制台日志处理器
    sh.setFormatter(formatter)  # 设置格式
    logger.addHandler(sh)  # 添加到logger

    return logger  # 返回logger对象