import torch
from loss import *
from utils import *


# 训练流程函数，包含主损失和辅助损失
# dataloader: 训练数据加载器
# model: 神经网络模型
# optimizer: 优化器
# criterion: 主损失函数（如BCELoss）
# criterion2: 辅助损失函数（如KLDivLoss）
# lamda: 辅助损失权重
def train_func(dataloader, model, optimizer, criterion, criterion2, lamda=0, epoch=0):
    t_loss = []  # 分类损失列表，记录每个batch的主损失
    s_loss = []  # KL损失列表，记录每个batch的辅助损失
    m_loss = []  # MAL损失列表，记录每个batch的MAL损失

    with torch.set_grad_enabled(True):  # 启用梯度计算
        model.train()  # 设置模型为训练模式
        for i, (v_input, t_input, label, multi_label) in enumerate(dataloader):
            # 重置VMRNN状态（如果模型使用VMRNN）
            if hasattr(model, 'reset_states'):
                model.reset_states()
                
            # v_input: [batch, seq_len, feat_dim]，视频特征输入
            # t_input: [batch, 2, 512]，文本token特征输入
            # label: [batch]，0/1标签，表示异常/正常
            # multi_label: [batch]，多标签，辅助任务
            seq_len = torch.sum(torch.max(torch.abs(v_input), dim=2)[0] > 0, 1)  # 计算每个样本的有效帧数，形状[batch]
            v_input = v_input[:, :torch.max(seq_len), :]  # 截断所有样本到最大有效帧数，形状[batch, max_seq, feat_dim]
            v_input = v_input.float().cuda(non_blocking=True)  # 转为float并搬到GPU
            t_input = t_input.float().cuda(non_blocking=True)  # token特征搬到GPU
            label = label.float().cuda(non_blocking=True)  # 标签搬到GPU
            multi_label = multi_label.cuda(non_blocking=True)  # 多标签搬到GPU

            logits, v_feat = model(v_input, seq_len)  # 前向传播，logits: [batch, seq, 1]，v_feat: [batch, seq, out_dim]
            
            # Prompt-Enhanced Learning（提示增强学习）
            logit_scale = model.logit_scale.exp()  # 获取logit缩放因子
            video_feat, token_feat, video_labels = get_cas(v_feat, t_input, logits, multi_label)  # 计算视频和token特征及标签
            v2t_logits, v2v_logits = create_logits(video_feat, token_feat, logit_scale)  # 计算视频-文本和视频-视频的logits
            ground_truth = torch.tensor(gen_label(video_labels), dtype=v_feat.dtype).cuda()  # 生成一组one-hot标签
            
            loss2 = KLV_loss(v2t_logits, ground_truth, criterion2)  # 计算KL损失
            loss1 = CLAS2(logits, label, seq_len, criterion)  # 计算主分类损失
            
            # 添加MAL损失 - 边际感知对抗损失（动态权重）
            loss_mal = MultiMarginContrastive(v_feat, label, seq_len, margin=0.15)

            # 动态权重调整 - 根据训练进度调整损失权重
            epoch_progress = epoch / 50.0  # 假设最大50轮

            # MAL损失权重随训练进度递减，早期更注重特征学习
            mal_weight = 0.02 * (1.0 - epoch_progress * 0.5)  # 从0.02递减到0.01

            # KL损失权重在中期达到峰值
            kl_weight = lamda * (1.0 + 0.5 * torch.sin(torch.tensor(epoch_progress * 3.14159)))

            # 计算总损失（动态权重分配）
            loss = loss1 + kl_weight * loss2 + mal_weight * loss_mal

            optimizer.zero_grad()  # 梯度清零
            loss.backward()  # 反向传播

            # 自适应梯度裁剪 - 根据梯度范数动态调整
            total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=float('inf'))
            if total_norm > 10.0:  # 如果梯度过大，进行裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)
            elif total_norm > 5.0:  # 中等梯度，轻微裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=8.0)

            optimizer.step()  # 优化器更新参数

            t_loss.append(loss1)  # 记录主损失
            s_loss.append(loss2)  # 记录辅助损失
            m_loss.append(loss_mal)  # 记录MAL损失

    # 返回平均主损失、辅助损失和MAL损失
    return sum(t_loss) / len(t_loss), sum(s_loss) / len(s_loss)
