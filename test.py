from sklearn.metrics import auc, roc_curve, confusion_matrix, precision_recall_curve  # 导入常用评估指标
import numpy as np  # 导入numpy用于数值计算
import torch  # 导入torch用于张量操作


# 计算正常样本的误报率（False Alarm Rate）
def cal_false_alarm(gt, preds, threshold=0.5):
    preds = list(preds.cpu().detach().numpy())  # 将预测张量转为numpy列表
    gt = list(gt.cpu().detach().numpy())  # 将标签张量转为numpy列表

    # 检查预测值是否包含NaN或Inf
    if np.isnan(preds).any() or np.isinf(preds).any():
        # 替换NaN/Inf值为阈值（避免错误）
        preds = np.nan_to_num(preds, nan=threshold, posinf=1.0, neginf=0.0)
    
    preds = np.repeat(preds, 16)  # 每个预测值扩展16倍，对齐帧级标签
    preds[preds < threshold] = 0  # 小于阈值的预测设为0
    preds[preds >= threshold] = 1  # 大于等于阈值的预测设为1
    tn, fp, fn, tp = confusion_matrix(gt, preds, labels=[0, 1]).ravel()  # 计算混淆矩阵

    far = fp / (fp + tn)  # 误报率=假正例/(假正例+真负例)

    return far  # 返回误报率


# 测试流程，评估模型在测试集上的表现
def test_func(dataloader, model, gt, dataset):
    with torch.no_grad():  # 测试时不计算梯度
        model.eval()  # 设置为评估模式
        pred = torch.zeros(0).cuda()  # 所有预测分数
        abnormal_preds = torch.zeros(0).cuda()  # 异常样本预测分数
        abnormal_labels = torch.zeros(0).cuda()  # 异常样本标签
        normal_preds = torch.zeros(0).cuda()  # 正常样本预测分数
        normal_labels = torch.zeros(0).cuda()  # 正常样本标签
        gt_tmp = torch.tensor(gt.copy()).cuda()  # 拷贝一份gt到GPU

        for i, (v_input, label) in enumerate(dataloader):  # 遍历测试集
            # 确保每个视频样本前重置VMRNN状态
            if hasattr(model, 'reset_states'):
                model.reset_states()
                
            v_input = v_input.float().cuda(non_blocking=True)  # 视频特征搬到GPU
            seq_len = torch.sum(torch.max(torch.abs(v_input), dim=2)[0] > 0, 1)  # 计算每个样本的有效帧数

            logits, _ = model(v_input, seq_len)  # 前向传播，logits: [batch, seq, 1]
            
            # 检查是否有NaN或无穷大值
            if torch.isnan(logits).any() or torch.isinf(logits).any():
                # 替换NaN和无穷大值为合理值
                logits = torch.nan_to_num(logits, nan=0.5, posinf=1.0, neginf=0.0)
            
            logits = torch.mean(logits, 0)  # 对batch维度取均值，得到[seq, 1]
            pred = torch.cat((pred, logits))  # 拼接所有预测分数
            labels = gt_tmp[: seq_len[0] * 16]  # 取当前样本的标签（帧级）
            if torch.sum(labels) == 0:  # 全部为正常
                normal_labels = torch.cat((normal_labels, labels))
                normal_preds = torch.cat((normal_preds, logits))
            else:  # 有异常
                abnormal_labels = torch.cat((abnormal_labels, labels))
                abnormal_preds = torch.cat((abnormal_preds, logits))
            gt_tmp = gt_tmp[seq_len[0] * 16:]  # 移动gt指针

        # 确保没有NaN或无穷大值
        pred_np = pred.cpu().detach().numpy()
        if np.isnan(pred_np).any() or np.isinf(pred_np).any():
            pred_np = np.nan_to_num(pred_np, nan=0.5, posinf=1.0, neginf=0.0)
            pred = torch.tensor(pred_np).cuda()
            
        pred = list(pred.cpu().detach().numpy())  # 所有预测分数转为numpy
        
        # 检查正常预测是否有NaN或无穷大值
        if torch.isnan(normal_preds).any() or torch.isinf(normal_preds).any():
            normal_preds = torch.nan_to_num(normal_preds, nan=0.5, posinf=1.0, neginf=0.0)
            
        n_far = cal_false_alarm(normal_labels, normal_preds)  # 计算正常样本误报率
        fpr, tpr, _ = roc_curve(list(gt), np.repeat(pred, 16))  # 计算ROC曲线
        roc_auc = auc(fpr, tpr)  # 计算AUC
        pre, rec, _ = precision_recall_curve(list(gt), np.repeat(pred, 16))  # 计算PR曲线
        pr_auc = auc(rec, pre)  # 计算PR-AUC

        if dataset == 'ucf-crime':  # UCF数据集返回ROC-AUC
            return roc_auc, n_far
        elif dataset == 'xd-violence':  # XD数据集返回PR-AUC
            return pr_auc, n_far
        elif dataset == 'shanghaiTech':  # 上海数据集返回ROC-AUC
            return roc_auc, n_far
        else:
            raise RuntimeError('Invalid dataset.')
