def build_config(dataset):
    cfg = type('', (), {})()  # 创建空对象用于存储配置
    if dataset in ['ucf', 'ucf-crime']:
        cfg.dataset = 'ucf-crime'  # 数据集名称
        cfg.model_name = 'ucf_'  # 模型名称前缀
        cfg.metrics = 'AUC'  # 评估指标
        # cfg.feat_prefix = '/data/pyj/feat/ucf-i3d'
        cfg.feat_prefix = r'E:\project\PEL4VAD-master (2)\PEL4VAD-master\PEL4VAD-master\PEL4VAD-master\feat\ucf-i3d'  # 特征文件路径
        cfg.train_list = './list/ucf/train.list'  # 训练集列表
        cfg.test_list = './list/ucf/test.list'  # 测试集列表
        cfg.token_feat = './list/ucf/ucf-prompt.npy'  # token特征文件
        cfg.gt = './list/ucf/ucf-gt.npy'  # ground truth标签
        # TCA settings
        cfg.win_size = 9  # 局部窗口大小
        cfg.gamma = 0.6  # 距离邻接参数
        cfg.bias = 0.2  # 距离邻接偏置
        cfg.norm = True  # 是否归一化
        # CC settings
        cfg.t_step = 9  # 分类器卷积核长度
        # training settings
        cfg.temp = 0.09  # softmax温度
        cfg.lamda = 1  # 辅助损失权重
        cfg.seed = 9  # 随机种子
        # test settings
        cfg.test_bs = 10  # 测试batch size
        cfg.smooth = 'slide'  # ['fixed': 10, slide': 7] 平滑方式
        cfg.kappa = 7  # 平滑窗口
        cfg.ckpt_path = './ckpt/ucf__8553.pkl'  # 预训练模型路径

    elif dataset in ['xd', 'xd-violence']:
        cfg.dataset = 'xd-violence'  # 数据集名称
        cfg.model_name = 'xd_'  # 模型名称前缀
        cfg.metrics = 'AP'  # 评估指标
        # cfg.feat_prefix = '/data/pyj/feat/xd-i3d'
        cfg.feat_prefix = r'E:\project\PEL4VAD-master (2)\PEL4VAD-master\PEL4VAD-master\PEL4VAD-master\feat\xd-i3d'  # 特征文件路径
        cfg.train_list = './list/xd/train.list'  # 训练集列表
        cfg.test_list = './list/xd/test.list'  # 测试集列表
        cfg.token_feat = './list/xd/xd-prompt.npy'  # token特征文件
        cfg.gt = './list/xd/xd-gt.npy'  # ground truth标签
        # TCA settings
        cfg.win_size = 9  # 局部窗口大小
        cfg.gamma = 0.06  # 距离邻接参数
        cfg.bias = 0.02  # 距离邻接偏置
        cfg.norm = False  # 是否归一化
        # CC settings
        cfg.t_step = 3  # 分类器卷积核长度
        # training settings
        cfg.temp = 0.05  # softmax温度
        cfg.lamda = 1  # 辅助损失权重
        cfg.seed = 4  # 随机种子
        # test settings
        cfg.test_bs = 5  # 测试batch size
        cfg.smooth = 'fixed'  # ['fixed': 8, slide': 3] 平滑方式
        cfg.kappa = 8  # 平滑窗口
        cfg.ckpt_path = './ckpt/xd__8442.pkl'  # 预训练模型路径

    elif dataset in ['sh', 'SHTech']:
        cfg.dataset = 'shanghaiTech'  # 数据集名称
        cfg.model_name = 'SH_'  # 模型名称前缀
        cfg.metrics = 'AUC'  # 评估指标
        # cfg.feat_prefix = '/data/pyj/feat/SHTech-i3d'
        cfg.feat_prefix = r'E:\project\PEL4VAD-master (2)\PEL4VAD-master\PEL4VAD-master\PEL4VAD-master\feat\SHTech-i3d'  # 特征文件路径
        cfg.train_list = './list/sh/train.list'  # 训练集列表
        cfg.test_list = './list/sh/test.list'  # 测试集列表
        cfg.token_feat = './list/sh/sh-prompt.npy'  # token特征文件
        cfg.abn_label = './list/sh/relabel.list'  # 异常标签文件
        cfg.gt = './list/sh/sh-gt.npy'  # ground truth标签
        # TCA settings
        cfg.win_size = 5  # 局部窗口大小
        cfg.gamma = 0.08  # 距离邻接参数
        cfg.bias = 0.1  # 距离邻接偏置
        cfg.norm = True  # 是否归一化
        # CC settings
        cfg.t_step = 3  # 分类器卷积核长度
        # training settings
        cfg.temp = 0.2  # softmax温度
        cfg.lamda = 9  # 辅助损失权重
        cfg.seed = 0  # 随机种子
        # test settings
        cfg.test_bs = 10  # 测试batch size
        cfg.smooth = 'slide'  # ['fixed': 5, slide': 3] 平滑方式
        cfg.kappa = 3  # 平滑窗口
        cfg.ckpt_path = './ckpt/SH__98.pkl'  # 预训练模型路径

    # base settings - 速度优化的超参数
    cfg.feat_dim = 1024  # 输入特征维度
    cfg.head_num = 1  # 保持单头注意力，提升速度
    cfg.hid_dim = 128  # 适中的隐藏层维度，平衡性能和速度
    cfg.out_dim = 256  # 适中的输出特征维度，减少计算量
    cfg.lr = 5e-4  # 适中的学习率
    cfg.dropout = 0.1  # 标准dropout率
    cfg.train_bs = 32  # 适中的batch size，平衡内存和速度
    cfg.max_seqlen = 200  # 最大序列长度
    cfg.max_epoch = 50    # 标准训练轮数
    cfg.workers = 4  # 减少worker数量，避免I/O瓶颈
    cfg.save_dir = './ckpt/'  # 模型保存路径
    cfg.logs_dir = './log_info.log'  # 日志文件路径

    # VMRNN settings - 速度优化的时序建模配置
    cfg.use_vmrnn = True  # 启用VMRNN与特征放大器协同工作
    cfg.rnn_depth = 1  # 单层VMRNN，提升速度
    cfg.d_state = 16  # 适中的状态维度

    # Feature Amplifier settings - 速度优化的TCA增强设置
    cfg.use_amplifier = True  # 是否使用特征放大器
    cfg.amplify_ratio = 1.0  # 不改变维度，减少计算
    cfg.amplifier_heads = 1  # 单头注意力，提升速度
    cfg.amplifier_dropout = 0.1  # 标准dropout率

    # 协同优化参数
    cfg.tca_temperature = 1.0  # TCA温度参数初始值
    cfg.feature_fusion_weight = 0.7  # 特征融合权重
    cfg.temporal_weight = 0.3  # 时序特征权重
    cfg.residual_scale = 0.1  # 残差连接缩放因子

    return cfg  # 返回配置对象
