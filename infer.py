import time  # 导入time用于计时
from utils import fixed_smooth, slide_smooth  # 导入平滑函数
from test import *  # 导入测试相关函数


def infer_func(model, dataloader, gt, logger, cfg):
    st = time.time()  # 记录开始时间
    with torch.no_grad():  # 推理时不计算梯度
        model.eval()  # 设置为评估模式
        pred = torch.zeros(0).cuda()  # 所有预测分数
        normal_preds = torch.zeros(0).cuda()  # 正常样本预测分数
        normal_labels = torch.zeros(0).cuda()  # 正常样本标签
        gt_tmp = torch.tensor(gt.copy()).cuda()  # 拷贝一份gt到GPU

        for i, (v_input, name) in enumerate(dataloader):  # 遍历测试集
            v_input = v_input.float().cuda(non_blocking=True)  # 视频特征搬到GPU
            seq_len = torch.sum(torch.max(torch.abs(v_input), dim=2)[0] > 0, 1)  # 计算每个样本的有效帧数
            logits, _ = model(v_input, seq_len)  # 前向传播，logits: [batch, seq, 1]
            logits = torch.mean(logits, 0)  # 对batch维度取均值，得到[seq, 1]
            logits = logits.squeeze(dim=-1)  # 去掉最后一维，得到[seq]

            seq = len(logits)  # 当前序列长度
            if cfg.smooth == 'fixed':  # 固定窗口平滑
                logits = fixed_smooth(logits, cfg.kappa)
            elif cfg.smooth == 'slide':  # 滑动窗口平滑
                logits = slide_smooth(logits, cfg.kappa)
            else:
                pass  # 不做平滑
            logits = logits[:seq]  # 截断到原始长度

            pred = torch.cat((pred, logits))  # 拼接所有预测分数
            labels = gt_tmp[: seq_len[0]*16]  # 取当前样本的标签（帧级）
            if torch.sum(labels) == 0:  # 全部为正常
                normal_labels = torch.cat((normal_labels, labels))
                normal_preds = torch.cat((normal_preds, logits))
            gt_tmp = gt_tmp[seq_len[0]*16:]  # 移动gt指针

        pred = list(pred.cpu().detach().numpy())  # 所有预测分数转为numpy
        far = cal_false_alarm(normal_labels, normal_preds)  # 计算正常样本误报率
        fpr, tpr, _ = roc_curve(list(gt), np.repeat(pred, 16))  # 计算ROC曲线
        roc_auc = auc(fpr, tpr)  # 计算AUC
        pre, rec, _ = precision_recall_curve(list(gt), np.repeat(pred, 16))  # 计算PR曲线
        pr_auc = auc(rec, pre)  # 计算PR-AUC

    time_elapsed = time.time() - st  # 计算耗时
    logger.info('offline AUC:{:.4f} AP:{:.4f} FAR:{:.4f} | Complete in {:.0f}m {:.0f}s\n'.format(
        roc_auc, pr_auc, far, time_elapsed // 60, time_elapsed % 60))
