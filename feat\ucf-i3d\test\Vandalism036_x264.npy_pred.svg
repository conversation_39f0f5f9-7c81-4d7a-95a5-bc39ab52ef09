<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Created with matplotlib (http://matplotlib.org/) -->
<svg height="216pt" version="1.1" viewBox="0 0 432 216" width="432pt" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
*{stroke-linecap:butt;stroke-linejoin:round;}
  </style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 216 
L 432 216 
L 432 0 
L 0 0 
z
" style="fill:#ffffff;"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 54 74.837647 
L 388.8 74.837647 
L 388.8 25.92 
L 54 25.92 
z
" style="fill:#ffffff;"/>
   </g>
   <g id="matplotlib.axis_1"/>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_1">
      <defs>
       <path d="M 0 0 
L -3.5 0 
" id="m53b75fecec" style="stroke:#000000;stroke-width:0.8;"/>
      </defs>
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="72.614118"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <defs>
       <path d="M 31.78125 66.40625 
Q 24.171875 66.40625 20.328125 58.90625 
Q 16.5 51.421875 16.5 36.375 
Q 16.5 21.390625 20.328125 13.890625 
Q 24.171875 6.390625 31.78125 6.390625 
Q 39.453125 6.390625 43.28125 13.890625 
Q 47.125 21.390625 47.125 36.375 
Q 47.125 51.421875 43.28125 58.90625 
Q 39.453125 66.40625 31.78125 66.40625 
z
M 31.78125 74.21875 
Q 44.046875 74.21875 50.515625 64.515625 
Q 56.984375 54.828125 56.984375 36.375 
Q 56.984375 17.96875 50.515625 8.265625 
Q 44.046875 -1.421875 31.78125 -1.421875 
Q 19.53125 -1.421875 13.0625 8.265625 
Q 6.59375 17.96875 6.59375 36.375 
Q 6.59375 54.828125 13.0625 64.515625 
Q 19.53125 74.21875 31.78125 74.21875 
z
" id="DejaVuSans-30"/>
      </defs>
      <g transform="translate(40.6375 76.413336)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_2">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="28.143529"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <defs>
       <path d="M 12.40625 8.296875 
L 28.515625 8.296875 
L 28.515625 63.921875 
L 10.984375 60.40625 
L 10.984375 69.390625 
L 28.421875 72.90625 
L 38.28125 72.90625 
L 38.28125 8.296875 
L 54.390625 8.296875 
L 54.390625 0 
L 12.40625 0 
z
" id="DejaVuSans-31"/>
      </defs>
      <g transform="translate(40.6375 31.942748)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
   </g>
   <g id="line2d_3">
    <path clip-path="url(#pe132e5563b)" d="M 54 72.427417 
L 64.9275 72.403501 
L 65.16 71.697685 
L 68.6475 71.697685 
L 68.88 70.994076 
L 72.3675 70.994076 
L 72.6 71.140232 
L 76.0875 71.140232 
L 76.32 67.733834 
L 79.8075 67.733834 
L 80.04 64.177566 
L 83.5275 64.177566 
L 83.76 64.39936 
L 94.6875 64.349887 
L 94.92 64.697236 
L 98.4075 64.697236 
L 98.64 64.948924 
L 102.1275 64.948924 
L 102.36 65.848094 
L 105.8475 65.848094 
L 106.08 66.05489 
L 109.5675 66.05489 
L 109.8 67.976659 
L 113.2875 67.976659 
L 113.52 70.909827 
L 117.0075 70.909827 
L 117.24 71.912537 
L 120.7275 71.912537 
L 120.96 72.396701 
L 124.4475 72.396701 
L 124.68 72.545899 
L 146.7675 72.458124 
L 147 72.195511 
L 150.4875 72.195511 
L 150.72 72.339412 
L 161.6475 72.347864 
L 161.88 72.025384 
L 165.3675 72.025384 
L 165.6 72.346936 
L 172.8075 72.326872 
L 173.04 70.172268 
L 176.5275 70.172268 
L 176.76 68.820281 
L 180.2475 68.820281 
L 180.48 64.665177 
L 183.9675 64.665177 
L 184.2 59.437434 
L 187.6875 59.437434 
L 187.92 43.986388 
L 191.4075 43.986388 
L 191.64 35.988234 
L 195.1275 35.988234 
L 195.36 33.277302 
L 198.8475 33.277302 
L 199.08 30.729817 
L 202.5675 30.729817 
L 202.8 29.238859 
L 206.2875 29.238859 
L 206.52 30.164756 
L 210.0075 30.164756 
L 210.24 31.828285 
L 213.7275 31.828285 
L 213.96 32.507905 
L 217.4475 32.507905 
L 217.68 36.303641 
L 221.1675 36.303641 
L 221.4 46.415611 
L 224.8875 46.415611 
L 225.12 51.014316 
L 228.6075 51.014316 
L 228.84 55.158114 
L 232.3275 55.158114 
L 232.56 60.83685 
L 236.0475 60.83685 
L 236.28 66.243873 
L 239.7675 66.243873 
L 240 67.82027 
L 243.4875 67.82027 
L 243.72 68.125943 
L 247.2075 68.125943 
L 247.44 68.453987 
L 250.9275 68.453987 
L 251.16 69.793803 
L 254.6475 69.793803 
L 254.88 70.982164 
L 258.3675 70.982164 
L 258.6 72.384688 
L 262.0875 72.384688 
L 262.5525 72.502766 
L 288.1275 72.585543 
L 288.36 72.295208 
L 291.8475 72.295208 
L 292.08 70.971143 
L 295.5675 70.971143 
L 295.8 68.398481 
L 299.2875 68.398481 
L 299.52 68.152558 
L 303.0075 68.152558 
L 303.24 67.850328 
L 306.7275 67.850328 
L 306.96 67.629948 
L 310.4475 67.629948 
L 310.68 68.037024 
L 314.1675 68.037024 
L 314.4 68.726287 
L 317.8875 68.726287 
L 318.12 70.751488 
L 321.6075 70.751488 
L 321.84 72.529293 
L 388.5675 72.421794 
L 388.5675 72.421794 
" style="fill:none;stroke:#ff7f0e;stroke-linecap:square;stroke-width:1.5;"/>
   </g>
   <g id="patch_3">
    <path d="M 54 74.837647 
L 54 25.92 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_4">
    <path d="M 388.8 74.837647 
L 388.8 25.92 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_5">
    <path d="M 54 74.837647 
L 388.8 74.837647 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_6">
    <path d="M 54 25.92 
L 388.8 25.92 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 54 133.538824 
L 388.8 133.538824 
L 388.8 84.621176 
L 54 84.621176 
z
" style="fill:#ffffff;"/>
   </g>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_3">
     <g id="line2d_4">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="131.315294"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0 -->
      <g transform="translate(40.6375 135.114513)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_5">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="86.844706"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 1 -->
      <g transform="translate(40.6375 90.643925)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
   </g>
   <g id="line2d_6">
    <path clip-path="url(#pea3fae5949)" d="M 54 131.260737 
L 79.8075 131.213687 
L 80.04 131.073483 
L 83.5275 131.073483 
L 83.76 130.707177 
L 87.2475 130.707177 
L 87.48 130.550391 
L 102.1275 130.502178 
L 102.36 130.905286 
L 109.5675 130.854751 
L 109.8 131.263247 
L 176.5275 131.226745 
L 176.76 131.036819 
L 180.2475 131.036819 
L 180.48 129.270316 
L 183.9675 129.270316 
L 184.2 125.093997 
L 187.6875 125.093997 
L 187.92 115.860214 
L 191.4075 115.860214 
L 191.64 109.116449 
L 195.1275 109.116449 
L 195.36 106.987081 
L 198.8475 106.987081 
L 199.08 104.976939 
L 202.5675 104.976939 
L 202.8 104.636153 
L 206.2875 104.636153 
L 206.52 107.776676 
L 210.0075 107.776676 
L 210.24 107.064538 
L 213.7275 107.064538 
L 214.1925 106.9425 
L 217.4475 106.9425 
L 217.68 107.493703 
L 221.1675 107.493703 
L 221.4 115.096948 
L 224.8875 115.096948 
L 225.12 112.567225 
L 228.6075 112.567225 
L 228.84 114.051143 
L 232.3275 114.051143 
L 232.56 117.688793 
L 236.0475 117.688793 
L 236.28 121.84183 
L 239.7675 121.84183 
L 240 123.350367 
L 243.4875 123.350367 
L 243.72 126.002151 
L 247.2075 126.002151 
L 247.44 126.778269 
L 250.9275 126.778269 
L 251.16 127.101039 
L 254.6475 127.101039 
L 254.88 127.892944 
L 258.3675 127.892944 
L 258.6 131.169679 
L 262.0875 131.169679 
L 262.5525 131.295046 
L 295.5675 131.201139 
L 295.8 130.461388 
L 299.2875 130.461388 
L 299.52 128.52881 
L 303.0075 128.52881 
L 303.24 129.207478 
L 306.7275 129.207478 
L 306.96 129.334833 
L 310.4475 129.334833 
L 310.68 130.671202 
L 314.1675 130.671202 
L 314.4 131.172486 
L 317.8875 131.172486 
L 318.12 131.301645 
L 388.5675 131.314097 
L 388.5675 131.314097 
" style="fill:none;stroke:#ff7f0e;stroke-linecap:square;stroke-width:1.5;"/>
   </g>
   <g id="patch_8">
    <path d="M 54 133.538824 
L 54 84.621176 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_9">
    <path d="M 388.8 133.538824 
L 388.8 84.621176 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_10">
    <path d="M 54 133.538824 
L 388.8 133.538824 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_11">
    <path d="M 54 84.621176 
L 388.8 84.621176 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_12">
    <path d="M 54 192.24 
L 388.8 192.24 
L 388.8 143.322353 
L 54 143.322353 
z
" style="fill:#ffffff;"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_1">
     <g id="line2d_7">
      <defs>
       <path d="M 0 0 
L 0 3.5 
" id="m7bd3984890" style="stroke:#000000;stroke-width:0.8;"/>
      </defs>
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(50.81875 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_8">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="100.5" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 200 -->
      <defs>
       <path d="M 19.1875 8.296875 
L 53.609375 8.296875 
L 53.609375 0 
L 7.328125 0 
L 7.328125 8.296875 
Q 12.9375 14.109375 22.625 23.890625 
Q 32.328125 33.6875 34.8125 36.53125 
Q 39.546875 41.84375 41.421875 45.53125 
Q 43.3125 49.21875 43.3125 52.78125 
Q 43.3125 58.59375 39.234375 62.25 
Q 35.15625 65.921875 28.609375 65.921875 
Q 23.96875 65.921875 18.8125 64.3125 
Q 13.671875 62.703125 7.8125 59.421875 
L 7.8125 69.390625 
Q 13.765625 71.78125 18.9375 73 
Q 24.125 74.21875 28.421875 74.21875 
Q 39.75 74.21875 46.484375 68.546875 
Q 53.21875 62.890625 53.21875 53.421875 
Q 53.21875 48.921875 51.53125 44.890625 
Q 49.859375 40.875 45.40625 35.40625 
Q 44.1875 33.984375 37.640625 27.21875 
Q 31.109375 20.453125 19.1875 8.296875 
z
" id="DejaVuSans-32"/>
      </defs>
      <g transform="translate(90.95625 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-32"/>
       <use x="63.623047" xlink:href="#DejaVuSans-30"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_9">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="147" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 400 -->
      <defs>
       <path d="M 37.796875 64.3125 
L 12.890625 25.390625 
L 37.796875 25.390625 
z
M 35.203125 72.90625 
L 47.609375 72.90625 
L 47.609375 25.390625 
L 58.015625 25.390625 
L 58.015625 17.1875 
L 47.609375 17.1875 
L 47.609375 0 
L 37.796875 0 
L 37.796875 17.1875 
L 4.890625 17.1875 
L 4.890625 26.703125 
z
" id="DejaVuSans-34"/>
      </defs>
      <g transform="translate(137.45625 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-34"/>
       <use x="63.623047" xlink:href="#DejaVuSans-30"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_10">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="193.5" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 600 -->
      <defs>
       <path d="M 33.015625 40.375 
Q 26.375 40.375 22.484375 35.828125 
Q 18.609375 31.296875 18.609375 23.390625 
Q 18.609375 15.53125 22.484375 10.953125 
Q 26.375 6.390625 33.015625 6.390625 
Q 39.65625 6.390625 43.53125 10.953125 
Q 47.40625 15.53125 47.40625 23.390625 
Q 47.40625 31.296875 43.53125 35.828125 
Q 39.65625 40.375 33.015625 40.375 
z
M 52.59375 71.296875 
L 52.59375 62.3125 
Q 48.875 64.0625 45.09375 64.984375 
Q 41.3125 65.921875 37.59375 65.921875 
Q 27.828125 65.921875 22.671875 59.328125 
Q 17.53125 52.734375 16.796875 39.40625 
Q 19.671875 43.65625 24.015625 45.921875 
Q 28.375 48.1875 33.59375 48.1875 
Q 44.578125 48.1875 50.953125 41.515625 
Q 57.328125 34.859375 57.328125 23.390625 
Q 57.328125 12.15625 50.6875 5.359375 
Q 44.046875 -1.421875 33.015625 -1.421875 
Q 20.359375 -1.421875 13.671875 8.265625 
Q 6.984375 17.96875 6.984375 36.375 
Q 6.984375 53.65625 15.1875 63.9375 
Q 23.390625 74.21875 37.203125 74.21875 
Q 40.921875 74.21875 44.703125 73.484375 
Q 48.484375 72.75 52.59375 71.296875 
z
" id="DejaVuSans-36"/>
      </defs>
      <g transform="translate(183.95625 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-36"/>
       <use x="63.623047" xlink:href="#DejaVuSans-30"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_11">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="240" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 800 -->
      <defs>
       <path d="M 31.78125 34.625 
Q 24.75 34.625 20.71875 30.859375 
Q 16.703125 27.09375 16.703125 20.515625 
Q 16.703125 13.921875 20.71875 10.15625 
Q 24.75 6.390625 31.78125 6.390625 
Q 38.8125 6.390625 42.859375 10.171875 
Q 46.921875 13.96875 46.921875 20.515625 
Q 46.921875 27.09375 42.890625 30.859375 
Q 38.875 34.625 31.78125 34.625 
z
M 21.921875 38.8125 
Q 15.578125 40.375 12.03125 44.71875 
Q 8.5 49.078125 8.5 55.328125 
Q 8.5 64.0625 14.71875 69.140625 
Q 20.953125 74.21875 31.78125 74.21875 
Q 42.671875 74.21875 48.875 69.140625 
Q 55.078125 64.0625 55.078125 55.328125 
Q 55.078125 49.078125 51.53125 44.71875 
Q 48 40.375 41.703125 38.8125 
Q 48.828125 37.15625 52.796875 32.3125 
Q 56.78125 27.484375 56.78125 20.515625 
Q 56.78125 9.90625 50.3125 4.234375 
Q 43.84375 -1.421875 31.78125 -1.421875 
Q 19.734375 -1.421875 13.25 4.234375 
Q 6.78125 9.90625 6.78125 20.515625 
Q 6.78125 27.484375 10.78125 32.3125 
Q 14.796875 37.15625 21.921875 38.8125 
z
M 18.3125 54.390625 
Q 18.3125 48.734375 21.84375 45.5625 
Q 25.390625 42.390625 31.78125 42.390625 
Q 38.140625 42.390625 41.71875 45.5625 
Q 45.3125 48.734375 45.3125 54.390625 
Q 45.3125 60.0625 41.71875 63.234375 
Q 38.140625 66.40625 31.78125 66.40625 
Q 25.390625 66.40625 21.84375 63.234375 
Q 18.3125 60.0625 18.3125 54.390625 
z
" id="DejaVuSans-38"/>
      </defs>
      <g transform="translate(230.45625 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-38"/>
       <use x="63.623047" xlink:href="#DejaVuSans-30"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_12">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="286.5" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 1000 -->
      <g transform="translate(273.775 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use x="63.623047" xlink:href="#DejaVuSans-30"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
       <use x="190.869141" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="333" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 1200 -->
      <g transform="translate(320.275 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use x="63.623047" xlink:href="#DejaVuSans-32"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
       <use x="190.869141" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_14">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="379.5" xlink:href="#m7bd3984890" y="192.24"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 1400 -->
      <g transform="translate(366.775 206.838437)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
       <use x="63.623047" xlink:href="#DejaVuSans-34"/>
       <use x="127.246094" xlink:href="#DejaVuSans-30"/>
       <use x="190.869141" xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_5">
     <g id="line2d_15">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="190.016471"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0 -->
      <g transform="translate(40.6375 193.815689)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_16">
      <g>
       <use style="stroke:#000000;stroke-width:0.8;" x="54" xlink:href="#m53b75fecec" y="145.545882"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 1 -->
      <g transform="translate(40.6375 149.345101)scale(0.1 -0.1)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
   </g>
   <g id="line2d_17">
    <path clip-path="url(#pae3178bafc)" d="M 54 185.994361 
L 61.2075 186.009971 
L 61.44 184.892979 
L 64.9275 184.892979 
L 65.16 185.389683 
L 68.6475 185.389683 
L 68.88 186.179897 
L 72.3675 186.179897 
L 72.6 188.256382 
L 76.0875 188.256382 
L 76.32 187.803943 
L 79.8075 187.803943 
L 80.04 188.142518 
L 83.5275 188.142518 
L 83.76 188.755613 
L 87.2475 188.755613 
L 87.48 188.353259 
L 90.9675 188.353259 
L 91.2 187.567216 
L 94.6875 187.567216 
L 94.92 188.53873 
L 98.4075 188.53873 
L 98.64 188.170864 
L 102.1275 188.170864 
L 102.36 188.336907 
L 105.8475 188.336907 
L 106.08 187.884004 
L 109.5675 187.884004 
L 109.8 188.964611 
L 117.0075 189.035224 
L 117.4725 189.136955 
L 139.3275 189.067201 
L 139.56 188.905003 
L 143.0475 188.905003 
L 143.28 188.656766 
L 146.7675 188.656766 
L 147 186.824708 
L 150.4875 186.824708 
L 150.72 188.362003 
L 157.9275 188.426756 
L 158.16 187.881179 
L 165.3675 187.858253 
L 165.6 188.032137 
L 169.0875 188.032137 
L 169.32 188.378614 
L 172.8075 188.378614 
L 173.04 187.344134 
L 176.5275 187.344134 
L 176.76 185.775214 
L 180.2475 185.775214 
L 180.48 180.951233 
L 183.9675 180.951233 
L 184.2 168.98621 
L 187.6875 168.98621 
L 187.92 153.794655 
L 191.4075 153.794655 
L 191.64 165.460322 
L 195.1275 165.460322 
L 195.36 168.948363 
L 198.8475 168.948363 
L 199.08 178.171007 
L 202.5675 178.171007 
L 202.8 184.163267 
L 206.2875 184.163267 
L 206.52 185.090951 
L 210.0075 185.090951 
L 210.24 187.778276 
L 213.7275 187.778276 
L 213.96 188.21887 
L 217.4475 188.21887 
L 217.68 186.806382 
L 221.1675 186.806382 
L 221.4 189.703944 
L 224.8875 189.703944 
L 225.12 186.235394 
L 228.6075 186.235394 
L 228.84 186.775471 
L 232.3275 186.775471 
L 232.56 183.789454 
L 236.0475 183.789454 
L 236.28 187.528708 
L 239.7675 187.528708 
L 240 185.873279 
L 243.4875 185.873279 
L 243.72 188.175895 
L 247.2075 188.175895 
L 247.44 188.339886 
L 250.9275 188.339886 
L 251.16 188.14445 
L 254.6475 188.14445 
L 254.88 188.595454 
L 258.3675 188.595454 
L 258.6 188.390761 
L 262.0875 188.390761 
L 262.32 187.534375 
L 265.8075 187.534375 
L 266.04 187.122334 
L 269.5275 187.122334 
L 269.76 189.002483 
L 273.2475 189.002483 
L 273.48 187.050605 
L 276.9675 187.050605 
L 277.2 187.355266 
L 280.6875 187.355266 
L 280.92 188.512109 
L 284.4075 188.512109 
L 284.64 184.449522 
L 288.1275 184.449522 
L 288.36 187.550615 
L 291.8475 187.550615 
L 292.08 177.780449 
L 295.5675 177.780449 
L 295.8 176.382628 
L 299.2875 176.382628 
L 299.52 187.872593 
L 306.7275 187.973662 
L 306.96 188.407867 
L 310.4475 188.407867 
L 310.68 187.527863 
L 314.1675 187.527863 
L 314.4 188.111021 
L 317.8875 188.111021 
L 318.12 188.787175 
L 325.3275 188.687863 
L 325.7925 188.579093 
L 329.0475 188.579093 
L 329.5125 188.692028 
L 336.4875 188.661659 
L 336.72 188.388837 
L 340.2075 188.388837 
L 340.44 187.961197 
L 343.9275 187.961197 
L 344.16 188.217114 
L 347.6475 188.217114 
L 347.88 188.854264 
L 355.0875 188.916068 
L 355.32 188.405569 
L 362.5275 188.355551 
L 362.76 188.681024 
L 366.2475 188.681024 
L 366.48 188.125285 
L 369.9675 188.125285 
L 370.2 186.661121 
L 373.6875 186.661121 
L 373.92 158.240936 
L 377.4075 158.240936 
L 377.64 185.874274 
L 381.1275 185.874274 
L 381.36 186.020128 
L 384.8475 186.020128 
L 385.08 188.648763 
L 388.5675 188.648763 
L 388.5675 188.648763 
" style="fill:none;stroke:#ff7f0e;stroke-linecap:square;stroke-width:1.5;"/>
   </g>
   <g id="patch_13">
    <path d="M 54 192.24 
L 54 143.322353 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_14">
    <path d="M 388.8 192.24 
L 388.8 143.322353 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_15">
    <path d="M 54 192.24 
L 388.8 192.24 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
   <g id="patch_16">
    <path d="M 54 143.322353 
L 388.8 143.322353 
" style="fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pe132e5563b">
   <rect height="48.917647" width="334.8" x="54" y="25.92"/>
  </clipPath>
  <clipPath id="pea3fae5949">
   <rect height="48.917647" width="334.8" x="54" y="84.621176"/>
  </clipPath>
  <clipPath id="pae3178bafc">
   <rect height="48.917647" width="334.8" x="54" y="143.322353"/>
  </clipPath>
 </defs>
</svg>
