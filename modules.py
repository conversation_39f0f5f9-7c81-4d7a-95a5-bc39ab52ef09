import torch  # 导入torch用于张量操作
import torch.nn.init as torch_init  # 导入权重初始化
import torch.nn as nn  # 导入神经网络模块
import math  # 导入数学库
from functools import partial  # 导入partial函数
import torch.nn.functional as F  # 导入常用函数

from layers import *  # 导入自定义层


# DropPath类，用于随机深度正则化


# DropPath类，用于随机深度正则化
class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # 二值化
        output = x.div(keep_prob) * random_tensor
        return output


# TCA增强器 - 专门增强TCA的核心能力
class FeatureAmplifier(nn.Module):
    """
    TCA增强器 - 增强版
    专门增强TCA模块的全局-局部注意力融合能力
    采用与TCA相同的设计理念，增强而不破坏原有机制
    新增多尺度时序建模和自适应特征选择机制
    """
    def __init__(self, d_model, amplify_ratio=1.0, num_heads=1, dropout=0.05):
        super(FeatureAmplifier, self).__init__()
        self.d_model = d_model
        self.num_heads = num_heads

        # 增强TCA的alpha融合机制 - 学习更好的全局-局部平衡
        self.alpha_enhance = nn.Parameter(torch.tensor(0.2))  # 提高初始值
        self.gamma_enhance = nn.Parameter(torch.tensor(0.1))  # 新增：时序权重参数

        # 多层特征重标定 - 更深层的特征增强
        self.feature_recalibration = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),  # 使用GELU激活
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, d_model),
            nn.Sigmoid()
        )

        # 多尺度时序对比增强 - 捕获不同时间尺度的异常模式
        self.temporal_contrast_multi = nn.ModuleList([
            nn.Conv1d(d_model, d_model // 3, kernel_size=k, padding=k//2, bias=False)
            for k in [3, 5, 7]  # 不同时间窗口
        ])
        self.temporal_fusion = nn.Conv1d(d_model, d_model, 1, bias=False)

        # 自适应特征选择 - 动态选择重要特征
        self.feature_selector = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化
            nn.Conv1d(d_model, d_model // 8, 1),
            nn.ReLU(),
            nn.Conv1d(d_model // 8, d_model, 1),
            nn.Sigmoid()
        )

        # 时序注意力机制 - 学习时序依赖关系
        self.temporal_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # 残差门控 - 控制增强程度
        self.residual_gate = nn.Parameter(torch.tensor(0.05))  # 适度提高初始值
        self.temporal_gate = nn.Parameter(torch.tensor(0.03))  # 时序门控

    def forward(self, x):
        """
        TCA增强前向传播 - 增强版
        Args:
            x: TCA输出的特征 [batch, seq, d_model]
        Returns:
            enhanced_x: 增强后的特征 [batch, seq, d_model]
        """
        batch_size, seq_len, d_model = x.shape

        # 1. 多层特征重标定 - 增强重要的特征维度
        recalibration_weights = self.feature_recalibration(x)  # [batch, seq, d_model]
        x_recalibrated = x * recalibration_weights

        # 2. 多尺度时序对比增强 - 捕获不同时间尺度的异常模式
        x_conv = x.permute(0, 2, 1)  # [batch, d_model, seq]

        # 多尺度卷积特征提取
        multi_scale_feats = []
        for conv in self.temporal_contrast_multi:
            feat = F.gelu(conv(x_conv))  # [batch, d_model//3, seq]
            multi_scale_feats.append(feat)

        # 拼接并融合多尺度特征
        multi_scale_concat = torch.cat(multi_scale_feats, dim=1)  # [batch, d_model, seq]
        x_contrast = self.temporal_fusion(multi_scale_concat)  # [batch, d_model, seq]
        x_contrast = x_contrast.permute(0, 2, 1)  # [batch, seq, d_model]

        # 3. 自适应特征选择 - 动态选择重要特征
        feature_importance = self.feature_selector(x_conv)  # [batch, d_model, 1]
        feature_importance = feature_importance.permute(0, 2, 1)  # [batch, 1, d_model]
        x_selected = x * feature_importance  # 广播应用特征选择

        # 4. 时序注意力增强 - 学习长距离时序依赖
        x_temporal, _ = self.temporal_attention(x, x, x)  # 自注意力

        # 5. 多路径自适应融合
        alpha = torch.sigmoid(self.alpha_enhance)
        gamma = torch.sigmoid(self.gamma_enhance)

        # 融合重标定特征和对比特征
        x_spatial = alpha * x_recalibrated + (1 - alpha) * x_contrast

        # 融合选择特征和时序特征
        x_temporal_fused = gamma * x_selected + (1 - gamma) * x_temporal

        # 6. 最终特征融合
        x_fused = 0.6 * x_spatial + 0.4 * x_temporal_fused

        # 7. 双重门控机制 - 精细控制增强程度
        residual_gate = torch.sigmoid(self.residual_gate)
        temporal_gate = torch.sigmoid(self.temporal_gate)

        # 分别控制空间和时序增强
        x_spatial_gated = x + residual_gate * (x_spatial - x)
        x_temporal_gated = x + temporal_gate * (x_temporal_fused - x)

        # 最终输出
        x_out = 0.7 * x_spatial_gated + 0.3 * x_temporal_gated

        # 8. 改进的归一化策略
        # 先进行layer norm稳定训练，再应用power norm增强表达能力
        x_out = F.layer_norm(x_out, x_out.shape[-1:])  # layer norm
        x_out = torch.sqrt(F.relu(x_out)) - torch.sqrt(F.relu(-x_out))  # power norm
        x_out = F.normalize(x_out, p=2, dim=-1)  # l2 norm

        return x_out


# VMRNN相关模块
class VSB(nn.Module):
    def __init__(self, hidden_dim, drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm_layer = norm_layer
        self.d_state = d_state
        
        # 改进：使用门控机制替代简单融合
        self.f_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 遗忘门
        self.i_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 输入门
        self.o_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 输出门
        self.c_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 候选状态
        
        # 频率自适应门控机制 - 动态调整特征频率响应
        self.freq_gate = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Linear(hidden_dim // 2, hidden_dim),
            nn.Sigmoid()
        )
        
        # 注意力机制增强
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        self.attn_scale = hidden_dim ** -0.5
        self.attn_dropout = nn.Dropout(0.2)  # 增加dropout率从0.1到0.2
        
        # 多头注意力设计
        self.num_heads = 4  # 设置头数
        self.head_dim = hidden_dim // self.num_heads
        self.q_proj = nn.Linear(hidden_dim, hidden_dim)
        self.k_proj = nn.Linear(hidden_dim, hidden_dim)
        self.v_proj = nn.Linear(hidden_dim, hidden_dim)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        self.proj_dropout = nn.Dropout(0.2)  # 添加投影后的dropout
        
        # 增强的状态空间层 - 使用更复杂的网络和残差连接
        self.ss_layer = nn.Sequential(
            nn.Linear(hidden_dim, d_state * 4),  # 扩大中间维度
            nn.LayerNorm(d_state * 4),
            nn.Dropout(0.2),  # 适度降低dropout率
            nn.GELU(),
            nn.Linear(d_state * 4, d_state * 2),
            nn.LayerNorm(d_state * 2),
            nn.Dropout(0.2),  # 适度降低dropout率
            nn.GELU(),
            nn.Linear(d_state * 2, hidden_dim),
            nn.Dropout(0.1)  # 最终dropout
        )

        # 状态空间的残差连接层
        self.ss_residual = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 自适应状态融合门控
        self.state_fusion_gate = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Sigmoid()
        )
        
        self.ln_1 = norm_layer(hidden_dim)
        self.ln_2 = norm_layer(hidden_dim)  # 额外的层归一化
        
    def _multi_head_attention(self, x):
        B, L, C = x.shape
        # 投影 query, key, value
        q = self.q_proj(x).view(B, L, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, L, head_dim]
        k = self.k_proj(x).view(B, L, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, L, head_dim]
        v = self.v_proj(x).view(B, L, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, L, head_dim]
        
        # 缩放点积注意力
        attn = (q @ k.transpose(-2, -1)) * self.attn_scale  # [B, num_heads, L, L]
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_dropout(attn)
        
        # 应用注意力并输出
        out = (attn @ v).transpose(1, 2).reshape(B, L, C)  # [B, L, C]
        out = self.out_proj(out)
        out = self.proj_dropout(out)  # 应用投影后的dropout
        return out
        
    def forward(self, x, hx=None, cx=None):
        B, L, C = x.shape
        
        shortcut = x
        x = self.ln_1(x)
        
        # 频率自适应门控 - 动态调整特征频率
        freq_gate = self.freq_gate(x)
        
        # 多头自注意力机制
        attn_out = self._multi_head_attention(x)
        
        if hx is not None:
            # 确保隐藏状态维度与输入匹配
            if hx.shape[1] != L:
                hx = F.interpolate(hx.permute(0, 2, 1), size=L, mode='linear', align_corners=True).permute(0, 2, 1)
            if cx is not None and cx.shape[1] != L:
                cx = F.interpolate(cx.permute(0, 2, 1), size=L, mode='linear', align_corners=True).permute(0, 2, 1)
                
            hx = self.ln_1(hx)
            combined = torch.cat((x, hx), dim=-1)
            
            # LSTM风格门控机制
            f = torch.sigmoid(self.f_gate(combined))
            i = torch.sigmoid(self.i_gate(combined))
            o = torch.sigmoid(self.o_gate(combined))
            c_tilde = torch.tanh(self.c_gate(combined))
            
            # 使用频率自适应门控增强状态更新
            f = f * freq_gate
            i = i * freq_gate
            
            if cx is not None:
                next_c = f * cx + i * c_tilde
            else:
                next_c = i * c_tilde
            
            # 结合注意力输出和门控输出
            gated_out = o * torch.tanh(next_c)
            x = gated_out + attn_out
            
        else:
            # 如果没有隐藏状态，仅使用注意力输出
            x = attn_out
            next_c = None
            
        # 增强的残差连接 - 多路径残差融合
        x = self.ln_2(x)

        # 主状态空间路径
        ss_out = self.ss_layer(x)

        # 残差状态空间路径
        ss_residual_out = self.ss_residual(x)

        # 自适应融合两个路径
        fusion_input = torch.cat([ss_out, ss_residual_out], dim=-1)
        fusion_gate = self.state_fusion_gate(fusion_input)
        ss_fused = fusion_gate * ss_out + (1 - fusion_gate) * ss_residual_out

        # 应用频率自适应门控
        ss_final = ss_fused * freq_gate

        # 最终输出 - 增强残差连接
        x = shortcut + self.drop_path(ss_final)
        
        return x, next_c


class VMRNNCell(nn.Module):
    def __init__(self, hidden_dim, depth=1, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.depth = depth
        
        # 支持不同层次的dropout
        drop_path_rates = [x.item() for x in torch.linspace(0, drop_path, depth)] if isinstance(drop_path, float) else drop_path
        
        # 自适应状态维度 - 随着深度增加而增加状态维度
        self.VSBs = nn.ModuleList([
            VSB(hidden_dim=hidden_dim, 
                drop_path=drop_path_rates[i],
                norm_layer=norm_layer,
                d_state=d_state * (i+1) // depth)  # 根据层深度调整状态维度
            for i in range(depth)
        ])
        
        # 跨层注意力门控机制 - 允许信息跨层直接传递
        self.cross_layer_gates = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.GELU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.Sigmoid()
            ) for _ in range(max(1, depth-1))
        ])
        
        # 层归一化 - 用于规范化层输出
        self.layer_norms = nn.ModuleList([
            norm_layer(hidden_dim)
            for _ in range(depth)
        ])
        
        # 层缩放系数 - 防止层数过深导致的梯度问题
        self.layer_scale = nn.Parameter(torch.ones(depth, 1, 1, 1) * 0.1)
        
        # 额外的特征整合层
        self.output_integration = nn.Sequential(
            nn.Linear(hidden_dim * depth, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(drop * 1.5),  # 增加dropout强度
            nn.GELU(),
            nn.Dropout(drop)  # 额外的dropout层
        ) if depth > 1 else nn.Identity()
        
    def forward(self, xt, hidden_states):
        B, L, C = xt.shape
        
        if hidden_states is None:
            # 初始化状态
            hx = torch.zeros(B, L, C).to(xt.device)
            cx = torch.zeros(B, L, C).to(xt.device)
            hidden_states = [(hx, cx) for _ in range(self.depth)]
        elif not isinstance(hidden_states[0], tuple):
            # 兼容旧版本的状态格式
            hidden_states = [hidden_states] * self.depth
            
        layer_outputs = []
        next_hidden_states = []
        
        # 第一层处理
        hx, cx = hidden_states[0]
        x, next_c = self.VSBs[0](xt, hx, cx)
        layer_outputs.append(x)
        next_hidden_states.append((x, next_c))
        
        # 处理后续层，实现跨层信息流动
        for i in range(1, self.depth):
            hx, cx = hidden_states[i]
            
            # 构建输入 - 结合前一层输出和跨层信息
            if i > 1:
                # 计算跨层信息流 - 允许较低层直接影响较高层
                cross_info = torch.zeros_like(layer_outputs[-1])
                for j in range(i-1):
                    # 计算跨层门控权重
                    gate_input = torch.cat([layer_outputs[j], layer_outputs[-1]], dim=-1)
                    gate = self.cross_layer_gates[j](gate_input)
                    cross_info = cross_info + gate * layer_outputs[j]
                
                # 结合当前层输入和跨层信息
                layer_input = self.layer_norms[i-1](layer_outputs[-1] + self.layer_scale[i-1] * cross_info)
            else:
                # 第二层直接使用第一层输出
                layer_input = layer_outputs[-1]
                
            # 处理当前层
            x, next_c = self.VSBs[i](layer_input, hx, cx)
            layer_outputs.append(x)
            next_hidden_states.append((x, next_c))
        
        # 融合多层输出
        if self.depth > 1:
            # 拼接所有层的输出并整合
            all_features = torch.cat(layer_outputs, dim=-1)
            integrated_output = self.output_integration(all_features)
            # 结合最后一层的输出和整合输出
            final_output = integrated_output + layer_outputs[-1]
        else:
            # 单层直接使用输出
            final_output = layer_outputs[-1]
            
        # 提取最后一层的隐藏状态和记忆单元
        _, last_cx = next_hidden_states[-1]
        if last_cx is None:
            last_cx = torch.zeros_like(final_output)
            
        # 更新和输出
        Ft = torch.sigmoid(final_output)
        cell = torch.tanh(final_output)
        Ct = Ft * (last_cx + cell)
        Ht = Ft * torch.tanh(Ct)
        
        return Ht, next_hidden_states


# 主编码器模块，包含时序自注意力、卷积降维和归一化
class XEncoder(nn.Module):
    def __init__(self, d_model, hid_dim, out_dim, n_heads, win_size, dropout, gamma, bias, norm=None, use_vmrnn=False, rnn_depth=1, d_state=16, use_amplifier=True, amplify_ratio=1.5):
        super(XEncoder, self).__init__()
        self.n_heads = n_heads  # 注意力头数
        self.win_size = win_size  # 局部窗口大小
        self.use_amplifier = use_amplifier  # 是否使用特征放大器

        self.self_attn = TCA(d_model, hid_dim, hid_dim, n_heads, norm)  # 时序自注意力层

        # 特征放大器 - 在TCA之后，降维之前
        if self.use_amplifier:
            self.feature_amplifier = FeatureAmplifier(
                d_model=d_model,
                amplify_ratio=amplify_ratio,
                num_heads=min(n_heads * 2, 8),  # 放大器使用更多注意力头
                dropout=dropout
            )

        self.linear1 = nn.Conv1d(d_model, d_model // 2, kernel_size=1)  # 1x1卷积降维
        self.linear2 = nn.Conv1d(d_model // 2, out_dim, kernel_size=1)  # 1x1卷积到输出维度
        self.dropout1 = nn.Dropout(dropout)  # dropout层
        self.dropout2 = nn.Dropout(dropout)
        self.norm1 = nn.LayerNorm(d_model)  # 第一层归一化
        self.norm2 = nn.LayerNorm(d_model)  # 第二层归一化
        self.loc_adj = DistanceAdj(gamma, bias)  # 距离邻接矩阵

        # VMRNN模块
        self.use_vmrnn = use_vmrnn
        if use_vmrnn:
            self.vmrnn = VMRNNCell(
                hidden_dim=d_model,
                depth=rnn_depth,
                drop=dropout,
                attn_drop=dropout,
                drop_path=dropout,
                norm_layer=nn.LayerNorm,
                d_state=d_state
            )
            self.hidden_states = None

    def forward(self, x, seq_len):
        # 保存原始输入用于最终残差连接
        x_original = x

        # 自注意力部分 - TCA处理
        adj = self.loc_adj(x.shape[0], x.shape[1])  # [batch, seq, seq] 距离邻接
        mask = self.get_mask(self.win_size, x.shape[1], seq_len)  # [n_head, batch, seq, seq] 局部mask
        x_tca = self.self_attn(x, mask, adj)  # TCA输出
        x_attn = x + x_tca  # 残差连接
        x_attn = self.norm1(x_attn)  # 归一化

        # 特征放大器处理 - 在TCA之后立即增强，保持特征新鲜度
        if self.use_amplifier:
            x_amplified = self.feature_amplifier(x_attn)  # TCA特征增强
            # 融合原始TCA输出和增强输出
            x_attn = 0.7 * x_attn + 0.3 * x_amplified

        # VMRNN处理 - 在特征增强后进行时序记忆建模
        if self.use_vmrnn:
            x_vmrnn, self.hidden_states = self.vmrnn(x_attn, self.hidden_states)
            # 增强的VMRNN融合策略
            x_attn = 0.8 * x_vmrnn + 0.2 * x_attn  # 保留部分非VMRNN特征
            x_attn = self.norm2(x_attn)  # VMRNN后归一化

        # 多模块协同增强 - 如果同时使用VMRNN和特征放大器
        if self.use_vmrnn and self.use_amplifier:
            # 二次特征放大，专门针对VMRNN输出
            x_second_amplified = self.feature_amplifier(x_attn)
            # 轻量级二次增强
            x_attn = x_attn + 0.1 * (x_second_amplified - x_attn)

        # 全局残差连接 - 确保信息流通
        x_attn = x_attn + 0.1 * x_original

        # 特征处理和降维
        x_combined = x_attn.permute(0, 2, 1)  # 转为[batch, channel, seq]

        # 改进的降维处理 - 添加残差连接
        x_mid = self.dropout1(F.gelu(self.linear1(x_combined)))  # 1x1卷积+激活+dropout
        x_e = self.dropout2(F.gelu(self.linear2(x_mid)))  # 1x1卷积到输出维度+激活+dropout

        return x_e, x_mid  # x_e: [batch, out_dim, seq], x_mid: [batch, d_model//2, seq]

    def get_mask(self, window_size, temporal_scale, seq_len):
        m = torch.zeros((temporal_scale, temporal_scale))  # [seq, seq] mask矩阵
        w_len = window_size  # 窗口长度
        for j in range(temporal_scale):
            for k in range(w_len):
                m[j, min(max(j - w_len // 2 + k, 0), temporal_scale - 1)] = 1.  # 局部窗口置1

        m = m.repeat(self.n_heads, len(seq_len), 1, 1).cuda()  # 扩展到多头和batch

        return m  # [n_head, batch, seq, seq]
        
    def reset_states(self):
        """重置VMRNN的状态"""
        if self.use_vmrnn:
            self.hidden_states = None
