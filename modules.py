import torch  # 导入torch用于张量操作
import torch.nn.init as torch_init  # 导入权重初始化
import torch.nn as nn  # 导入神经网络模块
import math  # 导入数学库
from functools import partial  # 导入partial函数
import torch.nn.functional as F  # 导入常用函数

from layers import *  # 导入自定义层


# DropPath类，用于随机深度正则化


# DropPath类，用于随机深度正则化
class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # 二值化
        output = x.div(keep_prob) * random_tensor
        return output


# TCA增强器 - 专门增强TCA的核心能力
class FeatureAmplifier(nn.Module):
    """
    TCA增强器 - 增强版
    专门增强TCA模块的全局-局部注意力融合能力
    采用与TCA相同的设计理念，增强而不破坏原有机制
    新增多尺度时序建模和自适应特征选择机制
    """
    def __init__(self, d_model, amplify_ratio=1.0, num_heads=1, dropout=0.05):
        super(FeatureAmplifier, self).__init__()
        self.d_model = d_model
        self.num_heads = num_heads

        # 增强TCA的alpha融合机制 - 学习更好的全局-局部平衡
        self.alpha_enhance = nn.Parameter(torch.tensor(0.2))  # 提高初始值
        self.gamma_enhance = nn.Parameter(torch.tensor(0.1))  # 新增：时序权重参数

        # 极简特征重标定 - 单层线性变换
        self.feature_recalibration = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.Sigmoid()
        )

        # 极简时序增强 - 单个卷积层
        self.temporal_enhance = nn.Conv1d(d_model, d_model, 3, padding=1, bias=False)

        # 简化的门控机制
        self.residual_gate = nn.Parameter(torch.tensor(0.1))  # 单一门控参数

    def forward(self, x):
        """
        TCA增强前向传播 - 速度优化版
        Args:
            x: TCA输出的特征 [batch, seq, d_model]
        Returns:
            enhanced_x: 增强后的特征 [batch, seq, d_model]
        """
        batch_size, seq_len, d_model = x.shape

        # 1. 轻量化特征重标定
        recalibration_weights = self.feature_recalibration(x)  # [batch, seq, d_model]
        x_recalibrated = x * recalibration_weights

        # 2. 简化的时序对比增强
        x_conv = x.permute(0, 2, 1)  # [batch, d_model, seq]
        x_contrast = F.relu(self.temporal_contrast(x_conv), inplace=True)  # [batch, d_model, seq]
        x_contrast = self.temporal_enhance(x_contrast)  # [batch, d_model, seq]
        x_contrast = x_contrast.permute(0, 2, 1)  # [batch, seq, d_model]

        # 3. 轻量化特征选择
        feature_importance = self.feature_selector(x_conv)  # [batch, d_model, 1]
        feature_importance = feature_importance.permute(0, 2, 1)  # [batch, 1, d_model]
        x_selected = x * feature_importance  # 广播应用特征选择

        # 4. 简化的特征融合
        alpha = torch.sigmoid(self.alpha_enhance)
        gamma = torch.sigmoid(self.gamma_enhance)

        # 两路径融合
        x_spatial = alpha * x_recalibrated + (1 - alpha) * x_contrast
        x_enhanced = gamma * x_spatial + (1 - gamma) * x_selected

        # 5. 单一门控机制
        residual_gate = torch.sigmoid(self.residual_gate)
        x_out = x + residual_gate * (x_enhanced - x)

        # 6. 简化的归一化策略
        x_out = F.normalize(x_out, p=2, dim=-1)  # 仅使用L2归一化

        return x_out


# VMRNN相关模块
class VSB(nn.Module):
    def __init__(self, hidden_dim, drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm_layer = norm_layer
        self.d_state = d_state
        
        # 改进：使用门控机制替代简单融合
        self.f_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 遗忘门
        self.i_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 输入门
        self.o_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 输出门
        self.c_gate = nn.Linear(hidden_dim * 2, hidden_dim)  # 候选状态
        
        # 简化的频率自适应门控
        self.freq_gate = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Sigmoid()
        )
        
        # 简化的注意力机制
        self.num_heads = 2  # 减少头数提升速度
        self.head_dim = hidden_dim // self.num_heads
        self.qkv_proj = nn.Linear(hidden_dim, hidden_dim * 3)  # 合并QKV投影
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        self.attn_scale = self.head_dim ** -0.5
        self.attn_dropout = nn.Dropout(0.1)
        
        # 简化的状态空间层
        self.ss_layer = nn.Sequential(
            nn.Linear(hidden_dim, d_state * 2),  # 减少中间维度
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(d_state * 2, hidden_dim)
        )
        
        self.ln_1 = norm_layer(hidden_dim)
        self.ln_2 = norm_layer(hidden_dim)  # 额外的层归一化
        
    def _multi_head_attention(self, x):
        B, L, C = x.shape
        # 合并的QKV投影，减少计算次数
        qkv = self.qkv_proj(x).view(B, L, 3, self.num_heads, self.head_dim)
        q, k, v = qkv.permute(2, 0, 3, 1, 4)  # [3, B, num_heads, L, head_dim]

        # 缩放点积注意力
        attn = (q @ k.transpose(-2, -1)) * self.attn_scale  # [B, num_heads, L, L]
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_dropout(attn)

        # 应用注意力并输出
        out = (attn @ v).transpose(1, 2).reshape(B, L, C)  # [B, L, C]
        out = self.out_proj(out)
        return out
        
    def forward(self, x, hx=None, cx=None):
        B, L, C = x.shape
        
        shortcut = x
        x = self.ln_1(x)
        
        # 频率自适应门控 - 动态调整特征频率
        freq_gate = self.freq_gate(x)
        
        # 多头自注意力机制
        attn_out = self._multi_head_attention(x)
        
        if hx is not None:
            # 确保隐藏状态维度与输入匹配
            if hx.shape[1] != L:
                hx = F.interpolate(hx.permute(0, 2, 1), size=L, mode='linear', align_corners=True).permute(0, 2, 1)
            if cx is not None and cx.shape[1] != L:
                cx = F.interpolate(cx.permute(0, 2, 1), size=L, mode='linear', align_corners=True).permute(0, 2, 1)
                
            hx = self.ln_1(hx)
            combined = torch.cat((x, hx), dim=-1)
            
            # LSTM风格门控机制
            f = torch.sigmoid(self.f_gate(combined))
            i = torch.sigmoid(self.i_gate(combined))
            o = torch.sigmoid(self.o_gate(combined))
            c_tilde = torch.tanh(self.c_gate(combined))
            
            # 使用频率自适应门控增强状态更新
            f = f * freq_gate
            i = i * freq_gate
            
            if cx is not None:
                next_c = f * cx + i * c_tilde
            else:
                next_c = i * c_tilde
            
            # 结合注意力输出和门控输出
            gated_out = o * torch.tanh(next_c)
            x = gated_out + attn_out
            
        else:
            # 如果没有隐藏状态，仅使用注意力输出
            x = attn_out
            next_c = None
            
        # 简化的残差连接
        x = self.ln_2(x)
        ss_out = self.ss_layer(x)

        # 应用频率自适应门控
        ss_out = ss_out * freq_gate

        # 最终输出
        x = shortcut + self.drop_path(ss_out)
        
        return x, next_c


class VMRNNCell(nn.Module):
    def __init__(self, hidden_dim, depth=1, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.depth = depth
        
        # 支持不同层次的dropout
        drop_path_rates = [x.item() for x in torch.linspace(0, drop_path, depth)] if isinstance(drop_path, float) else drop_path
        
        # 自适应状态维度 - 随着深度增加而增加状态维度
        self.VSBs = nn.ModuleList([
            VSB(hidden_dim=hidden_dim, 
                drop_path=drop_path_rates[i],
                norm_layer=norm_layer,
                d_state=d_state * (i+1) // depth)  # 根据层深度调整状态维度
            for i in range(depth)
        ])
        
        # 简化的层归一化
        self.layer_norms = nn.ModuleList([
            norm_layer(hidden_dim)
            for _ in range(depth)
        ])
        
    def forward(self, xt, hidden_states):
        B, L, C = xt.shape

        if hidden_states is None:
            # 初始化状态
            hx = torch.zeros(B, L, C).to(xt.device)
            cx = torch.zeros(B, L, C).to(xt.device)
            hidden_states = [(hx, cx) for _ in range(self.depth)]
        elif not isinstance(hidden_states[0], tuple):
            # 兼容旧版本的状态格式
            hidden_states = [hidden_states] * self.depth

        # 简化的多层处理
        x = xt
        next_hidden_states = []

        for i in range(self.depth):
            hx, cx = hidden_states[i]
            x, next_c = self.VSBs[i](x, hx, cx)
            next_hidden_states.append((x, next_c))

        # 简化的输出处理
        final_output = x

        # 提取最后一层的隐藏状态和记忆单元
        _, last_cx = next_hidden_states[-1]
        if last_cx is None:
            last_cx = torch.zeros_like(final_output)

        # 更新和输出
        Ft = torch.sigmoid(final_output)
        cell = torch.tanh(final_output)
        Ct = Ft * (last_cx + cell)
        Ht = Ft * torch.tanh(Ct)

        return Ht, next_hidden_states


# 主编码器模块，包含时序自注意力、卷积降维和归一化
class XEncoder(nn.Module):
    def __init__(self, d_model, hid_dim, out_dim, n_heads, win_size, dropout, gamma, bias, norm=None, use_vmrnn=False, rnn_depth=1, d_state=16, use_amplifier=True, amplify_ratio=1.5):
        super(XEncoder, self).__init__()
        self.n_heads = n_heads  # 注意力头数
        self.win_size = win_size  # 局部窗口大小
        self.use_amplifier = use_amplifier  # 是否使用特征放大器

        self.self_attn = TCA(d_model, hid_dim, hid_dim, n_heads, norm)  # 时序自注意力层

        # 特征放大器 - 在TCA之后，降维之前
        if self.use_amplifier:
            self.feature_amplifier = FeatureAmplifier(
                d_model=d_model,
                amplify_ratio=amplify_ratio,
                num_heads=min(n_heads * 2, 8),  # 放大器使用更多注意力头
                dropout=dropout
            )

        self.linear1 = nn.Conv1d(d_model, d_model // 2, kernel_size=1)  # 1x1卷积降维
        self.linear2 = nn.Conv1d(d_model // 2, out_dim, kernel_size=1)  # 1x1卷积到输出维度
        self.dropout1 = nn.Dropout(dropout)  # dropout层
        self.dropout2 = nn.Dropout(dropout)
        self.norm1 = nn.LayerNorm(d_model)  # 第一层归一化
        self.norm2 = nn.LayerNorm(d_model)  # 第二层归一化
        self.loc_adj = DistanceAdj(gamma, bias)  # 距离邻接矩阵

        # VMRNN模块
        self.use_vmrnn = use_vmrnn
        if use_vmrnn:
            self.vmrnn = VMRNNCell(
                hidden_dim=d_model,
                depth=rnn_depth,
                drop=dropout,
                attn_drop=dropout,
                drop_path=dropout,
                norm_layer=nn.LayerNorm,
                d_state=d_state
            )
            self.hidden_states = None

    def forward(self, x, seq_len):
        # 保存原始输入用于最终残差连接
        x_original = x

        # 自注意力部分 - TCA处理
        adj = self.loc_adj(x.shape[0], x.shape[1])  # [batch, seq, seq] 距离邻接
        mask = self.get_mask(self.win_size, x.shape[1], seq_len)  # [n_head, batch, seq, seq] 局部mask
        x_tca = self.self_attn(x, mask, adj)  # TCA输出
        x_attn = x + x_tca  # 残差连接
        x_attn = self.norm1(x_attn)  # 归一化

        # 特征放大器处理 - 在TCA之后立即增强，保持特征新鲜度
        if self.use_amplifier:
            x_amplified = self.feature_amplifier(x_attn)  # TCA特征增强
            # 融合原始TCA输出和增强输出
            x_attn = 0.7 * x_attn + 0.3 * x_amplified

        # VMRNN处理 - 在特征增强后进行时序记忆建模
        if self.use_vmrnn:
            x_vmrnn, self.hidden_states = self.vmrnn(x_attn, self.hidden_states)
            # 增强的VMRNN融合策略
            x_attn = 0.8 * x_vmrnn + 0.2 * x_attn  # 保留部分非VMRNN特征
            x_attn = self.norm2(x_attn)  # VMRNN后归一化

        # 多模块协同增强 - 如果同时使用VMRNN和特征放大器
        if self.use_vmrnn and self.use_amplifier:
            # 二次特征放大，专门针对VMRNN输出
            x_second_amplified = self.feature_amplifier(x_attn)
            # 轻量级二次增强
            x_attn = x_attn + 0.1 * (x_second_amplified - x_attn)

        # 全局残差连接 - 确保信息流通
        x_attn = x_attn + 0.1 * x_original

        # 特征处理和降维
        x_combined = x_attn.permute(0, 2, 1)  # 转为[batch, channel, seq]

        # 改进的降维处理 - 添加残差连接
        x_mid = self.dropout1(F.gelu(self.linear1(x_combined)))  # 1x1卷积+激活+dropout
        x_e = self.dropout2(F.gelu(self.linear2(x_mid)))  # 1x1卷积到输出维度+激活+dropout

        return x_e, x_mid  # x_e: [batch, out_dim, seq], x_mid: [batch, d_model//2, seq]

    def get_mask(self, window_size, temporal_scale, seq_len):
        m = torch.zeros((temporal_scale, temporal_scale))  # [seq, seq] mask矩阵
        w_len = window_size  # 窗口长度
        for j in range(temporal_scale):
            for k in range(w_len):
                m[j, min(max(j - w_len // 2 + k, 0), temporal_scale - 1)] = 1.  # 局部窗口置1

        m = m.repeat(self.n_heads, len(seq_len), 1, 1).cuda()  # 扩展到多头和batch

        return m  # [n_head, batch, seq, seq]
        
    def reset_states(self):
        """重置VMRNN的状态"""
        if self.use_vmrnn:
            self.hidden_states = None
